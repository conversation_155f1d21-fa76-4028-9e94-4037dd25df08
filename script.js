// 游戏管理器
class GameManager {
    constructor() {
        this.currentScreen = 'mainMenu';
        this.gameMode = 'classic';
        this.settings = {
            sound: true,
            speed: 'normal',
            theme: 'classic',
            particles: true,
            vibration: true,
            trail: false
        };
        this.leaderboards = {
            classic: [],
            adventure: [],
            timeattack: [],
            multiplayer: [],
            dailychallenge: [],
            survival: []
        };

        // 新增功能数据
        this.playerData = {
            coins: 0,
            unlockedSkins: ['classic'],
            currentSkin: 'classic',
            customLevels: [],
            dailyChallengeCompleted: false,
            lastChallengeDate: null,
            achievements: [],
            totalPlayTime: 0
        };

        this.skins = {
            classic: { name: '经典', price: 0, unlocked: true, colors: { head: '#2ecc71', body: '#27ae60' } },
            neon: { name: '霓虹', price: 100, unlocked: false, colors: { head: '#e74c3c', body: '#c0392b' } },
            galaxy: { name: '银河', price: 200, unlocked: false, colors: { head: '#9b59b6', body: '#8e44ad' } },
            golden: { name: '黄金', price: 500, unlocked: false, colors: { head: '#f1c40f', body: '#f39c12' } },
            rainbow: { name: '彩虹', price: 1000, unlocked: false, colors: { head: 'rainbow', body: 'rainbow' } },
            cyber: { name: '赛博', price: 300, unlocked: false, colors: { head: '#00ffff', body: '#0080ff' } }
        };

        this.dailyChallenges = [
            { name: '速度挑战', description: '在快速模式下达到500分', type: 'speed', target: 500 },
            { name: '长度挑战', description: '蛇身长度达到30', type: 'length', target: 30 },
            { name: '时间挑战', description: '生存5分钟', type: 'time', target: 300 },
            { name: '收集挑战', description: '收集20个特殊道具', type: 'powerups', target: 20 },
            { name: '完美挑战', description: '不使用护盾达到1000分', type: 'perfect', target: 1000 }
        ];

        this.loadSettings();
        this.loadLeaderboards();
        this.loadPlayerData();
        this.init();
    }

    init() {
        // 模拟加载时间
        setTimeout(() => {
            this.hideLoadingScreen();
            this.showScreen('mainMenu');
            this.setupGlobalEventListeners();
            this.applyTheme();

            // 显示加载完成提示
            console.log('🐍 超级贪吃蛇大冒险已加载完成！');

            // 预加载音频上下文（用户交互后才能使用）
            document.addEventListener('click', this.initAudioContext.bind(this), { once: true });
        }, 2000); // 2秒加载时间
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
            setTimeout(() => {
                loadingScreen.remove();
            }, 500);
        }
    }

    initAudioContext() {
        try {
            // 初始化音频上下文
            if (window.AudioContext || window.webkitAudioContext) {
                console.log('音频系统已准备就绪');
            }
        } catch (error) {
            console.log('音频系统初始化失败:', error);
        }
    }

    loadSettings() {
        const saved = localStorage.getItem('snakeSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    saveSettings() {
        localStorage.setItem('snakeSettings', JSON.stringify(this.settings));
    }

    loadLeaderboards() {
        const saved = localStorage.getItem('snakeLeaderboards');
        if (saved) {
            this.leaderboards = { ...this.leaderboards, ...JSON.parse(saved) };
        }
    }

    saveLeaderboards() {
        localStorage.setItem('snakeLeaderboards', JSON.stringify(this.leaderboards));
    }

    loadPlayerData() {
        const saved = localStorage.getItem('snakePlayerData');
        if (saved) {
            this.playerData = { ...this.playerData, ...JSON.parse(saved) };
        }
        // 更新皮肤解锁状态
        this.playerData.unlockedSkins.forEach(skinId => {
            if (this.skins[skinId]) {
                this.skins[skinId].unlocked = true;
            }
        });
    }

    savePlayerData() {
        localStorage.setItem('snakePlayerData', JSON.stringify(this.playerData));
    }

    setupGlobalEventListeners() {
        // ESC键返回主菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentScreen !== 'mainMenu') {
                this.showMainMenu();
            }
        });
    }

    showScreen(screenId) {
        // 隐藏所有屏幕
        document.querySelectorAll('.menu-screen, .game-screen').forEach(screen => {
            screen.classList.add('hidden');
        });

        // 显示指定屏幕
        const screen = document.getElementById(screenId);
        if (screen) {
            screen.classList.remove('hidden');
            this.currentScreen = screenId;
        }
    }

    // 皮肤商店相关方法
    showSkinShop() {
        this.showScreen('skinShopScreen');
        this.updateSkinShop();
    }

    updateSkinShop() {
        const skinGrid = document.getElementById('skinGrid');
        const coinsDisplay = document.getElementById('playerCoins');

        if (coinsDisplay) {
            coinsDisplay.textContent = this.playerData.coins;
        }

        if (skinGrid) {
            skinGrid.innerHTML = '';

            Object.entries(this.skins).forEach(([skinId, skin]) => {
                const skinItem = document.createElement('div');
                skinItem.className = `skin-item ${skin.unlocked ? 'unlocked' : 'locked'} ${this.playerData.currentSkin === skinId ? 'selected' : ''}`;

                skinItem.innerHTML = `
                    <div class="skin-preview" style="background: ${skin.colors.head}"></div>
                    <h3>${skin.name}</h3>
                    <div class="skin-price">${skin.unlocked ? (this.playerData.currentSkin === skinId ? '已选择' : '点击选择') : `🪙 ${skin.price}`}</div>
                `;

                if (skin.unlocked) {
                    skinItem.addEventListener('click', () => {
                        this.selectSkin(skinId);
                    });
                } else if (this.playerData.coins >= skin.price) {
                    skinItem.addEventListener('click', () => {
                        this.buySkin(skinId);
                    });
                }

                skinGrid.appendChild(skinItem);
            });
        }
    }

    selectSkin(skinId) {
        this.playerData.currentSkin = skinId;
        this.savePlayerData();
        this.updateSkinShop();
    }

    buySkin(skinId) {
        const skin = this.skins[skinId];
        if (this.playerData.coins >= skin.price) {
            this.playerData.coins -= skin.price;
            this.playerData.unlockedSkins.push(skinId);
            skin.unlocked = true;
            this.savePlayerData();
            this.updateSkinShop();
            this.showNotification(`🎉 成功购买 ${skin.name} 皮肤！`);
        }
    }

    // 关卡编辑器相关方法
    showLevelEditor() {
        this.showScreen('levelEditorScreen');
        this.initLevelEditor();
    }

    initLevelEditor() {
        if (!this.levelEditor) {
            this.levelEditor = new LevelEditor();
        }
    }

    // 每日挑战相关方法
    showDailyChallengeScreen() {
        this.showScreen('dailyChallengeScreen');
        this.updateDailyChallenge();
    }

    updateDailyChallenge() {
        const today = new Date().toDateString();

        // 检查是否是新的一天
        if (this.playerData.lastChallengeDate !== today) {
            this.playerData.dailyChallengeCompleted = false;
            this.playerData.lastChallengeDate = today;
            this.savePlayerData();
        }

        // 获取今日挑战
        const challengeIndex = new Date().getDate() % this.dailyChallenges.length;
        const todayChallenge = this.dailyChallenges[challengeIndex];

        const challengeCard = document.getElementById('todayChallenge');
        if (challengeCard) {
            challengeCard.innerHTML = `
                <h3>${todayChallenge.name}</h3>
                <p>${todayChallenge.description}</p>
                <div class="challenge-status ${this.playerData.dailyChallengeCompleted ? 'completed' : 'pending'}">
                    ${this.playerData.dailyChallengeCompleted ? '✅ 已完成' : '⏳ 待完成'}
                </div>
            `;
        }

        const rewardsDiv = document.getElementById('challengeRewards');
        if (rewardsDiv) {
            rewardsDiv.innerHTML = `
                <div class="reward-item">🪙 +100 金币</div>
                <div class="reward-item">⭐ +50 经验</div>
                <div class="reward-item">🎁 神秘奖励</div>
            `;
        }
    }

    startDailyChallenge() {
        const today = new Date().toDateString();
        const challengeIndex = new Date().getDate() % this.dailyChallenges.length;
        const challenge = this.dailyChallenges[challengeIndex];

        this.gameMode = 'dailychallenge';
        this.currentChallenge = challenge;
        this.showScreen('gameScreen');

        if (!game) {
            game = new EnhancedSnakeGame(this);
        } else {
            game.reset();
        }
    }

    // 多人游戏相关方法
    startLocalMultiplayer() {
        this.gameMode = 'multiplayer';
        this.showScreen('gameScreen');

        if (!game) {
            game = new MultiplayerSnakeGame(this);
        } else {
            game.reset();
        }
    }

    createRoom() {
        // 这里可以实现在线房间创建逻辑
        this.showNotification('🚧 在线功能正在开发中...');
    }

    joinRoom() {
        // 这里可以实现加入房间逻辑
        this.showNotification('🚧 在线功能正在开发中...');
    }

    // 生存模式
    startSurvivalMode() {
        this.gameMode = 'survival';
        this.showScreen('gameScreen');

        if (!game) {
            game = new EnhancedSnakeGame(this);
        } else {
            game.reset();
        }
    }

    // 通知系统
    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(46, 204, 113, 0.9);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// 关卡编辑器类
class LevelEditor {
    constructor() {
        this.canvas = document.getElementById('editorCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gridSize = 20;
        this.tileCount = this.canvas.width / this.gridSize;
        this.currentTool = 'wall';
        this.level = {
            walls: [],
            foods: [],
            powerups: [],
            spawn: { x: 15, y: 15 }
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.draw();
    }

    setupEventListeners() {
        // 工具选择
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentTool = btn.dataset.tool;
            });
        });

        // 画布点击
        this.canvas.addEventListener('click', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const x = Math.floor((e.clientX - rect.left) / this.gridSize);
            const y = Math.floor((e.clientY - rect.top) / this.gridSize);

            this.placeTile(x, y);
        });

        // 画布拖拽
        let isDrawing = false;
        this.canvas.addEventListener('mousedown', () => isDrawing = true);
        this.canvas.addEventListener('mouseup', () => isDrawing = false);
        this.canvas.addEventListener('mousemove', (e) => {
            if (isDrawing) {
                const rect = this.canvas.getBoundingClientRect();
                const x = Math.floor((e.clientX - rect.left) / this.gridSize);
                const y = Math.floor((e.clientY - rect.top) / this.gridSize);
                this.placeTile(x, y);
            }
        });
    }

    placeTile(x, y) {
        if (x < 0 || x >= this.tileCount || y < 0 || y >= this.tileCount) return;

        switch (this.currentTool) {
            case 'wall':
                this.addWall(x, y);
                break;
            case 'food':
                this.addFood(x, y);
                break;
            case 'powerup':
                this.addPowerup(x, y);
                break;
            case 'spawn':
                this.setSpawn(x, y);
                break;
            case 'erase':
                this.eraseTile(x, y);
                break;
        }

        this.draw();
    }

    addWall(x, y) {
        const exists = this.level.walls.find(w => w.x === x && w.y === y);
        if (!exists) {
            this.level.walls.push({ x, y });
        }
    }

    addFood(x, y) {
        this.eraseTile(x, y);
        this.level.foods.push({ x, y, type: 'normal' });
    }

    addPowerup(x, y) {
        this.eraseTile(x, y);
        this.level.powerups.push({ x, y, type: 'speed' });
    }

    setSpawn(x, y) {
        this.level.spawn = { x, y };
    }

    eraseTile(x, y) {
        this.level.walls = this.level.walls.filter(w => !(w.x === x && w.y === y));
        this.level.foods = this.level.foods.filter(f => !(f.x === x && f.y === y));
        this.level.powerups = this.level.powerups.filter(p => !(p.x === x && p.y === y));
    }

    draw() {
        // 清空画布
        this.ctx.fillStyle = '#1a1a2e';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        for (let i = 0; i <= this.tileCount; i++) {
            const pos = i * this.gridSize;
            this.ctx.beginPath();
            this.ctx.moveTo(pos, 0);
            this.ctx.lineTo(pos, this.canvas.height);
            this.ctx.stroke();

            this.ctx.beginPath();
            this.ctx.moveTo(0, pos);
            this.ctx.lineTo(this.canvas.width, pos);
            this.ctx.stroke();
        }

        // 绘制墙壁
        this.ctx.fillStyle = '#34495e';
        this.level.walls.forEach(wall => {
            this.ctx.fillRect(wall.x * this.gridSize, wall.y * this.gridSize, this.gridSize, this.gridSize);
        });

        // 绘制食物
        this.ctx.fillStyle = '#e74c3c';
        this.level.foods.forEach(food => {
            this.ctx.beginPath();
            this.ctx.arc(
                food.x * this.gridSize + this.gridSize/2,
                food.y * this.gridSize + this.gridSize/2,
                this.gridSize/3,
                0, Math.PI * 2
            );
            this.ctx.fill();
        });

        // 绘制道具
        this.ctx.fillStyle = '#f1c40f';
        this.level.powerups.forEach(powerup => {
            this.ctx.fillRect(
                powerup.x * this.gridSize + 2,
                powerup.y * this.gridSize + 2,
                this.gridSize - 4,
                this.gridSize - 4
            );
        });

        // 绘制起点
        this.ctx.fillStyle = '#2ecc71';
        this.ctx.beginPath();
        this.ctx.arc(
            this.level.spawn.x * this.gridSize + this.gridSize/2,
            this.level.spawn.y * this.gridSize + this.gridSize/2,
            this.gridSize/2 - 2,
            0, Math.PI * 2
        );
        this.ctx.fill();
    }

    clearLevel() {
        this.level = {
            walls: [],
            foods: [],
            powerups: [],
            spawn: { x: 15, y: 15 }
        };
        this.draw();
    }

    saveLevel() {
        const levelName = prompt('请输入关卡名称:');
        if (levelName) {
            const levelData = {
                name: levelName,
                data: JSON.parse(JSON.stringify(this.level)),
                created: new Date().toISOString()
            };

            gameManager.playerData.customLevels.push(levelData);
            gameManager.savePlayerData();
            gameManager.showNotification(`关卡 "${levelName}" 已保存！`);
        }
    }

    loadLevel() {
        const levels = gameManager.playerData.customLevels;
        if (levels.length === 0) {
            gameManager.showNotification('没有保存的关卡');
            return;
        }

        const levelNames = levels.map((level, index) => `${index + 1}. ${level.name}`).join('\n');
        const choice = prompt(`选择要加载的关卡:\n${levelNames}\n\n请输入序号:`);

        if (choice && !isNaN(choice)) {
            const index = parseInt(choice) - 1;
            if (index >= 0 && index < levels.length) {
                this.level = JSON.parse(JSON.stringify(levels[index].data));
                this.draw();
                gameManager.showNotification(`关卡 "${levels[index].name}" 已加载！`);
            }
        }
    }

    testLevel() {
        // 保存当前关卡到临时存储
        localStorage.setItem('tempLevel', JSON.stringify(this.level));

        // 切换到游戏模式测试
        gameManager.gameMode = 'custom';
        gameManager.showScreen('gameScreen');

        if (!game) {
            game = new EnhancedSnakeGame(gameManager);
        } else {
            game.reset();
        }

        // 加载自定义关卡数据
        game.loadCustomLevel(this.level);
    }
}

// 多人游戏类
class MultiplayerSnakeGame extends EnhancedSnakeGame {
    constructor(gameManager) {
        super(gameManager);
        this.player2 = {
            snake: [{ x: Math.floor(this.tileCount / 4), y: Math.floor(this.tileCount / 2) }],
            dx: 0,
            dy: 0,
            score: 0,
            color: { head: '#e74c3c', body: '#c0392b' }
        };
        this.setupPlayer2Controls();
    }

    setupPlayer2Controls() {
        // 玩家2使用WASD控制
        document.addEventListener('keydown', (e) => {
            if (!this.gameRunning || this.gamePaused) return;

            switch(e.key.toLowerCase()) {
                case 'w':
                    if (this.player2.dy !== 1) {
                        this.player2.dx = 0;
                        this.player2.dy = -1;
                    }
                    break;
                case 's':
                    if (this.player2.dy !== -1) {
                        this.player2.dx = 0;
                        this.player2.dy = 1;
                    }
                    break;
                case 'a':
                    if (this.player2.dx !== 1) {
                        this.player2.dx = -1;
                        this.player2.dy = 0;
                    }
                    break;
                case 'd':
                    if (this.player2.dx !== -1) {
                        this.player2.dx = 1;
                        this.player2.dy = 0;
                    }
                    break;
            }
        });
    }

    update() {
        if (!this.gameRunning || this.gamePaused) return;

        // 更新玩家1
        super.update();

        // 更新玩家2
        if (this.player2.dx !== 0 || this.player2.dy !== 0) {
            const head2 = {
                x: this.player2.snake[0].x + this.player2.dx,
                y: this.player2.snake[0].y + this.player2.dy
            };

            // 检查玩家2碰撞
            if (this.checkPlayer2Collision(head2)) {
                this.gameOver('player2');
                return;
            }

            this.player2.snake.unshift(head2);

            // 检查玩家2是否吃到食物
            if (head2.x === this.food.x && head2.y === this.food.y) {
                this.player2.score += this.getFoodPoints();
                this.generateFood();
            } else {
                this.player2.snake.pop();
            }
        }
    }

    checkPlayer2Collision(head) {
        // 边界碰撞
        if (head.x < 0 || head.x >= this.tileCount ||
            head.y < 0 || head.y >= this.tileCount) {
            return true;
        }

        // 自身碰撞
        if (this.player2.snake.some(segment => segment.x === head.x && segment.y === head.y)) {
            return true;
        }

        // 与玩家1碰撞
        if (this.snake.some(segment => segment.x === head.x && segment.y === head.y)) {
            return true;
        }

        return false;
    }

    drawSnake() {
        // 绘制玩家1
        super.drawSnake();

        // 绘制玩家2
        for (let i = 0; i < this.player2.snake.length; i++) {
            const segment = this.player2.snake[i];
            const isHead = i === 0;

            this.ctx.save();
            this.ctx.translate(segment.x * this.gridSize + this.gridSize/2, segment.y * this.gridSize + this.gridSize/2);

            if (isHead) {
                this.drawPlayer2Head();
            } else {
                this.drawPlayer2Body();
            }

            this.ctx.restore();
        }
    }

    drawPlayer2Head() {
        const size = this.gridSize - 2;
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, this.player2.color.head);
        gradient.addColorStop(1, this.player2.color.body);

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 眼睛
        this.ctx.fillStyle = '#2c3e50';
        this.ctx.beginPath();
        this.ctx.arc(-size/6, -size/6, 2, 0, Math.PI * 2);
        this.ctx.arc(size/6, -size/6, 2, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawPlayer2Body() {
        const size = this.gridSize - 4;
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, this.player2.color.head);
        gradient.addColorStop(1, this.player2.color.body);

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();
    }

    gameOver(loser) {
        this.gameRunning = false;
        this.playSound('gameover');
        this.updateButtons();

        const winner = loser === 'player1' ? '玩家2' : '玩家1';
        this.showMultiplayerGameOverModal(winner);
    }

    showMultiplayerGameOverModal(winner) {
        const gameOverDiv = document.createElement('div');
        gameOverDiv.className = 'game-over';
        gameOverDiv.innerHTML = `
            <h2>🎉 ${winner} 获胜！</h2>
            <div class="multiplayer-scores">
                <div class="player-score">
                    <h3>玩家1</h3>
                    <p>得分: ${this.score}</p>
                    <p>长度: ${this.snake.length}</p>
                </div>
                <div class="player-score">
                    <h3>玩家2</h3>
                    <p>得分: ${this.player2.score}</p>
                    <p>长度: ${this.player2.snake.length}</p>
                </div>
            </div>
            <div class="game-over-buttons">
                <button onclick="game.resetGame()">再来一局</button>
                <button onclick="gameManager.showScreen('multiplayerScreen')">返回</button>
                <button onclick="gameManager.showMainMenu()">主菜单</button>
            </div>
        `;

        document.body.appendChild(gameOverDiv);
    }
}

// 增强的贪吃蛇游戏类
class EnhancedSnakeGame {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.effectsLayer = document.getElementById('effectsLayer');

        // UI元素
        this.scoreElement = document.getElementById('score');
        this.levelElement = document.getElementById('level');
        this.lengthElement = document.getElementById('length');
        this.timeElement = document.getElementById('time');
        this.statusElement = document.getElementById('game-status');

        // 游戏设置
        this.gridSize = 20;
        this.tileCount = this.canvas.width / this.gridSize;

        // 游戏状态
        this.reset();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateUI();
        this.draw();
        console.log('增强贪吃蛇游戏初始化完成');
    }

    reset() {
        // 基本游戏状态
        this.snake = [{ x: Math.floor(this.tileCount / 2), y: Math.floor(this.tileCount / 2) }];
        this.food = {};
        this.specialItems = [];
        this.obstacles = [];
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.level = 1;
        this.gameRunning = false;
        this.gamePaused = false;
        this.gameStartTime = null;
        this.gameTime = 0;

        // 特殊状态
        this.hasShield = false;
        this.speedBoost = false;
        this.speedBoostTime = 0;
        this.invulnerable = false;
        this.invulnerableTime = 0;
        this.freezeTime = 0;
        this.magnetTime = 0;
        this.trailEffect = false;

        // 动画和效果
        this.snakeSegments = [];
        this.particles = [];
        this.animations = [];

        // 成就系统
        this.achievements = {
            firstFood: false,
            length10: false,
            length25: false,
            length50: false,
            score1000: false,
            score5000: false,
            score10000: false,
            speedDemon: false,
            survivor: false,
            perfectStart: false
        };

        // 统计数据
        this.stats = {
            foodEaten: 0,
            powerupsCollected: 0,
            timesShielded: 0,
            perfectMoves: 0,
            totalPlayTime: 0
        };

        // 根据游戏模式设置
        this.setupGameMode();
        this.generateFood();
        this.initializeSnakeSegments();
    }

    setupGameMode() {
        const mode = this.gameManager.gameMode;

        switch (mode) {
            case 'classic':
                // 经典模式无特殊设置
                break;
            case 'adventure':
                this.setupAdventureLevel();
                break;
            case 'timeattack':
                this.timeLimit = 120; // 2分钟限时
                break;
        }
    }

    setupAdventureLevel() {
        // 根据关卡设置障碍物
        this.obstacles = [];
        const level = this.level;

        if (level >= 2) {
            // 添加边界障碍
            for (let i = 5; i < this.tileCount - 5; i++) {
                if (i % 3 === 0) {
                    this.obstacles.push({ x: i, y: 5 });
                    this.obstacles.push({ x: i, y: this.tileCount - 6 });
                }
            }
        }

        if (level >= 3) {
            // 添加中央十字障碍
            const center = Math.floor(this.tileCount / 2);
            for (let i = center - 3; i <= center + 3; i++) {
                this.obstacles.push({ x: center, y: i });
                this.obstacles.push({ x: i, y: center });
            }
        }
    }

    initializeSnakeSegments() {
        this.snakeSegments = this.snake.map((segment, index) => ({
            ...segment,
            targetX: segment.x,
            targetY: segment.y,
            currentX: segment.x * this.gridSize,
            currentY: segment.y * this.gridSize,
            scale: 1,
            rotation: 0,
            isHead: index === 0
        }));
    }
    
    setupEventListeners() {
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            // 防止页面滚动
            if(['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space'].includes(e.key)) {
                e.preventDefault();
            }

            // 暂停/继续
            if (e.key === ' ' || e.key === 'Space') {
                if (this.gameRunning) {
                    this.togglePause();
                }
                return;
            }

            if (!this.gameRunning || this.gamePaused) return;

            this.changeDirection(e.key);
        });

        // 按钮控制
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());

        // 移动端控制
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const direction = btn.dataset.direction;

                if (!this.gameRunning) {
                    this.startGame();
                }

                if (this.gamePaused) return;

                this.changeDirection(direction);
            });
        });
    }

    changeDirection(input) {
        let newDx = this.dx;
        let newDy = this.dy;

        switch(input) {
            case 'ArrowUp':
            case 'up':
                if (this.dy !== 1) {
                    newDx = 0;
                    newDy = -1;
                }
                break;
            case 'ArrowDown':
            case 'down':
                if (this.dy !== -1) {
                    newDx = 0;
                    newDy = 1;
                }
                break;
            case 'ArrowLeft':
            case 'left':
                if (this.dx !== 1) {
                    newDx = -1;
                    newDy = 0;
                }
                break;
            case 'ArrowRight':
            case 'right':
                if (this.dx !== -1) {
                    newDx = 1;
                    newDy = 0;
                }
                break;
        }

        // 平滑方向改变动画
        if (newDx !== this.dx || newDy !== this.dy) {
            this.dx = newDx;
            this.dy = newDy;
            this.createDirectionChangeEffect();
        }
    }

    createDirectionChangeEffect() {
        const head = this.snakeSegments[0];
        if (head) {
            this.createParticles(head.currentX + this.gridSize/2, head.currentY + this.gridSize/2, '#4ecdc4', 5);
        }
    }

    startGame() {
        if (!this.gameRunning) {
            this.gameRunning = true;
            this.gamePaused = false;
            this.gameStartTime = Date.now();

            // 如果蛇还没有移动方向，给它一个初始方向
            if (this.dx === 0 && this.dy === 0) {
                this.dx = 1;
                this.dy = 0;
            }

            this.updateButtons();
            this.statusElement.textContent = '游戏进行中 - 使用方向键或空格键控制';
            this.playSound('start');
            this.gameLoop();
        }
    }

    togglePause() {
        if (this.gameRunning) {
            this.gamePaused = !this.gamePaused;
            this.updateButtons();
            this.statusElement.textContent = this.gamePaused ? '游戏已暂停 - 按空格键继续' : '游戏进行中 - 使用方向键或空格键控制';
            this.playSound('pause');

            if (this.gamePaused) {
                this.showPauseOverlay();
            } else {
                this.hidePauseOverlay();
                this.gameLoop();
            }
        }
    }

    showPauseOverlay() {
        // 移除已存在的暂停覆盖层
        this.hidePauseOverlay();

        const overlay = document.createElement('div');
        overlay.id = 'pauseOverlay';
        overlay.className = 'pause-overlay';
        overlay.innerHTML = `
            <div class="pause-content">
                <h2>⏸️ 游戏暂停</h2>
                <p>按空格键继续游戏</p>
                <div class="pause-stats">
                    <div>当前得分: ${this.score}</div>
                    <div>蛇身长度: ${this.snake.length}</div>
                    <div>当前关卡: ${this.level}</div>
                </div>
                <button onclick="game.togglePause()">继续游戏</button>
                <button onclick="game.resetGame()">重新开始</button>
                <button onclick="gameManager.showMainMenu()">返回主菜单</button>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    hidePauseOverlay() {
        const overlay = document.getElementById('pauseOverlay');
        if (overlay) {
            overlay.remove();
        }
    }

    resetGame() {
        this.reset();
        this.updateButtons();
        this.updateUI();
        this.statusElement.textContent = '点击"开始游戏"开始';
        this.draw();
        this.clearEffects();
    }

    updateButtons() {
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');

        startBtn.disabled = this.gameRunning;
        pauseBtn.disabled = !this.gameRunning;
        pauseBtn.textContent = this.gamePaused ? '继续' : '暂停';
    }

    updateUI() {
        // 分数动画效果
        if (this.scoreElement.textContent !== this.score.toString()) {
            this.scoreElement.textContent = this.score;
            this.scoreElement.style.animation = 'none';
            setTimeout(() => {
                this.scoreElement.style.animation = 'scoreUpdate 0.3s ease-out';
            }, 10);
        }

        this.levelElement.textContent = this.level;
        this.lengthElement.textContent = this.snake.length;

        if (this.gameStartTime) {
            this.gameTime = Math.floor((Date.now() - this.gameStartTime) / 1000);

            if (this.gameManager.gameMode !== 'timeattack') {
                const minutes = Math.floor(this.gameTime / 60);
                const seconds = this.gameTime % 60;
                this.timeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // 更新状态指示器
        this.updateStatusIndicators();
    }

    updateStatusIndicators() {
        // 清除之前的状态指示器
        const existingIndicators = document.querySelectorAll('.status-indicator');
        existingIndicators.forEach(indicator => indicator.remove());

        const gameHeader = document.querySelector('.game-header');
        if (!gameHeader) return;

        // 护盾状态
        if (this.hasShield) {
            const shieldIndicator = document.createElement('div');
            shieldIndicator.className = 'status-indicator shield-indicator';
            shieldIndicator.innerHTML = '🛡️ 护盾激活';
            shieldIndicator.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(52, 152, 219, 0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
                animation: shieldGlow 2s infinite;
            `;
            gameHeader.appendChild(shieldIndicator);
        }

        // 速度提升状态
        if (this.speedBoost) {
            const speedIndicator = document.createElement('div');
            speedIndicator.className = 'status-indicator speed-indicator';
            speedIndicator.innerHTML = `⚡ 加速 (${Math.ceil(this.speedBoostTime / 60)}s)`;
            speedIndicator.style.cssText = `
                position: absolute;
                top: 10px;
                right: ${this.hasShield ? '120px' : '10px'};
                background: rgba(241, 196, 15, 0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
                animation: speedBoost 1s infinite;
            `;
            gameHeader.appendChild(speedIndicator);
        }
    }

    clearEffects() {
        // 清除所有特效
        this.particles = [];
        this.animations = [];
        this.effectsLayer.innerHTML = '';

        // 移除游戏结束弹窗
        const gameOverDiv = document.querySelector('.game-over');
        if (gameOverDiv) {
            gameOverDiv.remove();
        }

        const nameInputModal = document.querySelector('.name-input-modal');
        if (nameInputModal) {
            nameInputModal.remove();
        }

        // 移除暂停覆盖层
        this.hidePauseOverlay();

        // 清除状态指示器
        const statusIndicators = document.querySelectorAll('.status-indicator');
        statusIndicators.forEach(indicator => indicator.remove());
    }
    
    generateFood() {
        let attempts = 0;
        do {
            this.food = {
                x: Math.floor(Math.random() * this.tileCount),
                y: Math.floor(Math.random() * this.tileCount),
                type: this.getFoodType(),
                animation: 0
            };
            attempts++;
        } while (this.isPositionOccupied(this.food.x, this.food.y) && attempts < 100);

        // 生成特殊道具
        if (Math.random() < 0.1 && this.specialItems.length < 2) {
            this.generateSpecialItem();
        }
    }

    getFoodType() {
        const rand = Math.random();
        if (rand < 0.8) return 'normal';
        if (rand < 0.95) return 'special';
        return 'bonus';
    }

    generateSpecialItem() {
        let attempts = 0;
        let item;

        do {
            item = {
                x: Math.floor(Math.random() * this.tileCount),
                y: Math.floor(Math.random() * this.tileCount),
                type: this.getSpecialItemType(),
                duration: 300 + Math.random() * 300, // 5-10秒
                animation: 0
            };
            attempts++;
        } while (this.isPositionOccupied(item.x, item.y) && attempts < 50);

        if (attempts < 50) {
            this.specialItems.push(item);
        }
    }

    getSpecialItemType() {
        const types = ['speed', 'shield', 'score', 'teleport', 'freeze', 'magnet', 'bomb'];
        return types[Math.floor(Math.random() * types.length)];
    }

    // 加载自定义关卡
    loadCustomLevel(levelData) {
        this.obstacles = levelData.walls || [];
        this.specialItems = levelData.powerups || [];

        // 设置起点
        if (levelData.spawn) {
            this.snake = [{ x: levelData.spawn.x, y: levelData.spawn.y }];
            this.initializeSnakeSegments();
        }

        // 添加预设食物
        if (levelData.foods && levelData.foods.length > 0) {
            const randomFood = levelData.foods[Math.floor(Math.random() * levelData.foods.length)];
            this.food = { ...randomFood, animation: 0 };
        }
    }

    isPositionOccupied(x, y) {
        // 检查是否与蛇身重叠
        for (let segment of this.snake) {
            if (segment.x === x && segment.y === y) return true;
        }

        // 检查是否与障碍物重叠
        for (let obstacle of this.obstacles) {
            if (obstacle.x === x && obstacle.y === y) return true;
        }

        // 检查是否与食物重叠
        if (this.food && this.food.x === x && this.food.y === y) return true;

        // 检查是否与其他特殊道具重叠
        for (let item of this.specialItems) {
            if (item.x === x && item.y === y) return true;
        }

        return false;
    }
    
    update() {
        if (!this.gameRunning || this.gamePaused) return;

        // 更新特殊状态时间
        this.updateSpecialStates();

        // 更新特殊道具
        this.updateSpecialItems();

        // 如果时间冻结，跳过移动
        if (this.freezeTime > 0) {
            this.updateUI();
            return;
        }

        const head = { x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy };

        // 检查碰撞
        if (this.checkCollision(head)) {
            if (this.hasShield) {
                // 护盾保护，消耗护盾
                this.hasShield = false;
                this.createShieldEffect();
                this.playSound('shield');
            } else if (!this.invulnerable) {
                this.gameOver();
                return;
            }
        }

        this.snake.unshift(head);

        // 检查是否吃到食物
        if (head.x === this.food.x && head.y === this.food.y) {
            this.eatFood();
        } else {
            this.snake.pop();
        }

        // 检查是否吃到特殊道具
        this.checkSpecialItems(head);

        // 更新蛇身动画
        this.updateSnakeAnimation();

        // 更新UI
        this.updateUI();

        // 检查关卡升级
        this.checkLevelUp();

        // 检查限时模式
        this.checkTimeLimit();
    }

    updateSpecialStates() {
        if (this.speedBoostTime > 0) {
            this.speedBoostTime--;
            if (this.speedBoostTime <= 0) {
                this.speedBoost = false;
            }
        }

        if (this.invulnerableTime > 0) {
            this.invulnerableTime--;
            if (this.invulnerableTime <= 0) {
                this.invulnerable = false;
            }
        }

        if (this.freezeTime > 0) {
            this.freezeTime--;
        }

        if (this.magnetTime > 0) {
            this.magnetTime--;
            this.attractFood();
        }
    }

    updateSpecialItems() {
        this.specialItems = this.specialItems.filter(item => {
            item.duration--;
            item.animation += 0.1;
            return item.duration > 0;
        });
    }

    checkCollision(head) {
        // 边界碰撞
        if (head.x < 0 || head.x >= this.tileCount ||
            head.y < 0 || head.y >= this.tileCount) {
            return true;
        }

        // 自身碰撞
        if (this.snake.some(segment => segment.x === head.x && segment.y === head.y)) {
            return true;
        }

        // 障碍物碰撞
        if (this.obstacles.some(obstacle => obstacle.x === head.x && obstacle.y === head.y)) {
            return true;
        }

        return false;
    }

    eatFood() {
        const points = this.getFoodPoints();
        this.score += points;
        this.stats.foodEaten++;

        // 获得金币
        const coins = Math.floor(points / 10);
        this.gameManager.playerData.coins += coins;
        this.gameManager.savePlayerData();

        this.createScorePopup(this.food.x * this.gridSize, this.food.y * this.gridSize, points);
        this.createParticles(this.food.x * this.gridSize + this.gridSize/2, this.food.y * this.gridSize + this.gridSize/2, this.getFoodColor(), 8);

        if (coins > 0) {
            this.createCoinPopup(this.food.x * this.gridSize, this.food.y * this.gridSize - 20, coins);
        }

        this.playSound('eat');
        this.vibrate(50); // 轻微振动
        this.generateFood();

        // 检查成就
        this.checkAchievements();
    }

    checkAchievements() {
        // 第一次吃食物
        if (!this.achievements.firstFood && this.stats.foodEaten === 1) {
            this.achievements.firstFood = true;
            this.showAchievement('🍎 第一口！开始你的冒险吧！');
            this.grantAchievementReward(10);
        }

        // 长度成就
        if (!this.achievements.length10 && this.snake.length >= 10) {
            this.achievements.length10 = true;
            this.showAchievement('🐍 小蛇成长！长度达到10');
            this.grantAchievementReward(25);
        }

        if (!this.achievements.length25 && this.snake.length >= 25) {
            this.achievements.length25 = true;
            this.showAchievement('🐍 蛇王之路！长度达到25');
            this.grantAchievementReward(50);
        }

        if (!this.achievements.length50 && this.snake.length >= 50) {
            this.achievements.length50 = true;
            this.showAchievement('🐍 传说巨蛇！长度达到50');
            this.grantAchievementReward(100);
        }

        // 分数成就
        if (!this.achievements.score1000 && this.score >= 1000) {
            this.achievements.score1000 = true;
            this.showAchievement('💯 千分达人！分数突破1000');
        }

        if (!this.achievements.score5000 && this.score >= 5000) {
            this.achievements.score5000 = true;
            this.showAchievement('🏆 高分玩家！分数突破5000');
        }

        if (!this.achievements.score10000 && this.score >= 10000) {
            this.achievements.score10000 = true;
            this.showAchievement('👑 分数之王！分数突破10000');
        }

        // 速度恶魔成就
        if (!this.achievements.speedDemon && this.speedBoost && this.snake.length >= 20) {
            this.achievements.speedDemon = true;
            this.showAchievement('⚡ 速度恶魔！在加速状态下达到20长度');
        }

        // 生存者成就
        if (!this.achievements.survivor && this.gameTime >= 300) { // 5分钟
            this.achievements.survivor = true;
            this.showAchievement('⏰ 生存专家！游戏时间超过5分钟');
        }

        // 完美开局成就
        if (!this.achievements.perfectStart && this.score >= 500 && this.gameTime <= 60) {
            this.achievements.perfectStart = true;
            this.showAchievement('🚀 完美开局！1分钟内得分500');
        }
    }

    grantAchievementReward(coins) {
        this.gameManager.playerData.coins += coins;
        this.gameManager.savePlayerData();

        // 创建奖励特效
        const head = this.snake[0];
        this.createCoinPopup(
            head.x * this.gridSize + this.gridSize/2,
            head.y * this.gridSize - 30,
            coins
        );
    }

    getFoodPoints() {
        switch (this.food.type) {
            case 'normal': return 10;
            case 'special': return 50;
            case 'bonus': return 100;
            default: return 10;
        }
    }

    getFoodColor() {
        switch (this.food.type) {
            case 'normal': return '#e74c3c';
            case 'special': return '#9b59b6';
            case 'bonus': return '#f1c40f';
            default: return '#e74c3c';
        }
    }

    checkSpecialItems(head) {
        for (let i = this.specialItems.length - 1; i >= 0; i--) {
            const item = this.specialItems[i];
            if (item.x === head.x && item.y === head.y) {
                this.collectSpecialItem(item);
                this.specialItems.splice(i, 1);
            }
        }
    }

    collectSpecialItem(item) {
        this.stats.powerupsCollected++;
        this.createParticles(item.x * this.gridSize + this.gridSize/2, item.y * this.gridSize + this.gridSize/2, '#4ecdc4', 12);
        this.playSound('powerup');

        // 创建特殊收集效果
        this.createCollectionEffect(item.x * this.gridSize + this.gridSize/2, item.y * this.gridSize + this.gridSize/2, item.type);

        switch (item.type) {
            case 'speed':
                this.speedBoost = true;
                this.speedBoostTime = 300; // 5秒
                this.showAchievement('⚡ 速度提升！移动更快5秒');
                break;
            case 'shield':
                this.hasShield = true;
                this.stats.timesShielded++;
                this.showAchievement('🛡️ 获得护盾！免疫一次碰撞');
                break;
            case 'score':
                this.score += 200;
                this.showAchievement('💎 额外得分！+200分');
                break;
            case 'teleport':
                this.teleportSnake();
                this.showAchievement('🌀 传送！随机移动到新位置');
                break;
            case 'freeze':
                this.freezeTime = 180; // 3秒
                this.showAchievement('❄️ 时间冻结！3秒内无需移动');
                break;
            case 'magnet':
                this.magnetTime = 300; // 5秒
                this.showAchievement('🧲 磁力！自动吸引食物');
                break;
            case 'bomb':
                this.explodeBomb(item.x, item.y);
                this.showAchievement('💥 炸弹！清除周围障碍');
                break;
        }
    }

    createCollectionEffect(x, y, type) {
        const effect = document.createElement('div');
        effect.style.position = 'absolute';
        effect.style.left = x + 'px';
        effect.style.top = y + 'px';
        effect.style.width = '30px';
        effect.style.height = '30px';
        effect.style.borderRadius = '50%';
        effect.style.pointerEvents = 'none';
        effect.style.zIndex = '1000';

        let color, symbol;
        switch (type) {
            case 'speed':
                color = '#f1c40f';
                symbol = '⚡';
                break;
            case 'shield':
                color = '#3498db';
                symbol = '🛡️';
                break;
            case 'score':
                color = '#e74c3c';
                symbol = '💎';
                break;
        }

        effect.style.background = `radial-gradient(circle, ${color}, transparent)`;
        effect.style.animation = 'collectEffect 1s ease-out forwards';
        effect.textContent = symbol;
        effect.style.display = 'flex';
        effect.style.alignItems = 'center';
        effect.style.justifyContent = 'center';
        effect.style.fontSize = '20px';

        this.effectsLayer.appendChild(effect);

        setTimeout(() => {
            if (effect.parentNode) {
                effect.parentNode.removeChild(effect);
            }
        }, 1000);
    }

    updateSnakeAnimation() {
        // 更新蛇身段的动画
        this.snakeSegments.forEach((segment, index) => {
            const target = this.snake[index];
            if (target) {
                segment.targetX = target.x;
                segment.targetY = target.y;

                // 平滑移动动画
                const lerpFactor = 0.3;
                segment.currentX += (target.x * this.gridSize - segment.currentX) * lerpFactor;
                segment.currentY += (target.y * this.gridSize - segment.currentY) * lerpFactor;

                // 呼吸效果
                segment.scale = 1 + Math.sin(Date.now() * 0.01 + index * 0.5) * 0.05;
            }
        });

        // 添加新的蛇身段
        while (this.snakeSegments.length < this.snake.length) {
            const lastSegment = this.snake[this.snakeSegments.length];
            this.snakeSegments.push({
                ...lastSegment,
                targetX: lastSegment.x,
                targetY: lastSegment.y,
                currentX: lastSegment.x * this.gridSize,
                currentY: lastSegment.y * this.gridSize,
                scale: 1,
                rotation: 0,
                isHead: false
            });
        }

        // 移除多余的蛇身段
        if (this.snakeSegments.length > this.snake.length) {
            this.snakeSegments.splice(this.snake.length);
        }
    }

    checkLevelUp() {
        const newLevel = Math.floor(this.score / 500) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            this.showAchievement(`🎉 升级到第 ${this.level} 关！`);
            this.playSound('levelup');

            if (this.gameManager.gameMode === 'adventure') {
                this.setupAdventureLevel();
            }

            // 关卡奖励
            this.grantLevelReward();
        }
    }

    grantLevelReward() {
        // 每升级一关给予奖励
        if (this.level % 3 === 0) {
            // 每3关给护盾
            this.hasShield = true;
            this.showAchievement('🎁 关卡奖励：获得护盾！');
        } else if (this.level % 2 === 0) {
            // 每2关给额外分数
            this.score += 100;
            this.showAchievement('🎁 关卡奖励：+100分！');
        }
    }

    checkTimeLimit() {
        if (this.gameManager.gameMode === 'timeattack' && this.timeLimit) {
            const remainingTime = this.timeLimit - this.gameTime;

            if (remainingTime <= 0) {
                this.showAchievement('⏰ 时间到！游戏结束');
                this.gameOver();
                return;
            }

            // 时间警告
            if (remainingTime <= 10 && remainingTime > 0 && !this.timeWarningShown) {
                this.timeWarningShown = true;
                this.showAchievement('⚠️ 时间不多了！');
                this.playSound('warning');
            }

            // 更新时间显示
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            this.timeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            this.timeElement.style.color = remainingTime <= 30 ? '#e74c3c' : '#4ecdc4';
        }
    }
    
    draw() {
        // 清空画布
        this.ctx.fillStyle = '#1a1a2e';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格（可选）
        this.drawGrid();

        // 绘制障碍物
        this.drawObstacles();

        // 绘制食物
        this.drawFood();

        // 绘制特殊道具
        this.drawSpecialItems();

        // 绘制蛇
        this.drawSnake();

        // 更新粒子效果
        this.updateParticles();

        // 更新动画
        this.updateAnimations();
    }

    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
        this.ctx.lineWidth = 1;

        for (let i = 0; i <= this.tileCount; i++) {
            const pos = i * this.gridSize;
            this.ctx.beginPath();
            this.ctx.moveTo(pos, 0);
            this.ctx.lineTo(pos, this.canvas.height);
            this.ctx.stroke();

            this.ctx.beginPath();
            this.ctx.moveTo(0, pos);
            this.ctx.lineTo(this.canvas.width, pos);
            this.ctx.stroke();
        }
    }

    drawObstacles() {
        this.ctx.fillStyle = '#34495e';
        this.ctx.strokeStyle = '#2c3e50';
        this.ctx.lineWidth = 2;

        for (let obstacle of this.obstacles) {
            const x = obstacle.x * this.gridSize;
            const y = obstacle.y * this.gridSize;

            this.ctx.fillRect(x + 1, y + 1, this.gridSize - 2, this.gridSize - 2);
            this.ctx.strokeRect(x + 1, y + 1, this.gridSize - 2, this.gridSize - 2);
        }
    }

    drawFood() {
        if (!this.food) return;

        const x = this.food.x * this.gridSize;
        const y = this.food.y * this.gridSize;
        const centerX = x + this.gridSize / 2;
        const centerY = y + this.gridSize / 2;

        // 食物动画
        this.food.animation += 0.1;
        const pulse = 1 + Math.sin(this.food.animation) * 0.1;
        const size = (this.gridSize - 4) * pulse;

        this.ctx.save();
        this.ctx.translate(centerX, centerY);

        // 根据食物类型绘制不同样式
        switch (this.food.type) {
            case 'normal':
                this.drawApple(size);
                break;
            case 'special':
                this.drawGrape(size);
                break;
            case 'bonus':
                this.drawGoldenApple(size);
                break;
        }

        this.ctx.restore();
    }

    drawApple(size) {
        // 绘制苹果
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, '#ff6b6b');
        gradient.addColorStop(1, '#e74c3c');

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 苹果叶子
        this.ctx.fillStyle = '#27ae60';
        this.ctx.beginPath();
        this.ctx.ellipse(-2, -size/3, 3, 6, -Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawGrape(size) {
        // 绘制葡萄
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, '#bb6bd9');
        gradient.addColorStop(1, '#9b59b6');

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 葡萄光泽
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.beginPath();
        this.ctx.arc(-size/6, -size/6, size/6, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawGoldenApple(size) {
        // 绘制金苹果
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, '#f1c40f');
        gradient.addColorStop(1, '#f39c12');

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 金色光芒
        this.ctx.strokeStyle = 'rgba(241, 196, 15, 0.5)';
        this.ctx.lineWidth = 2;
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            this.ctx.beginPath();
            this.ctx.moveTo(Math.cos(angle) * size/2, Math.sin(angle) * size/2);
            this.ctx.lineTo(Math.cos(angle) * size, Math.sin(angle) * size);
            this.ctx.stroke();
        }
    }
    
    drawSpecialItems() {
        for (let item of this.specialItems) {
            const x = item.x * this.gridSize;
            const y = item.y * this.gridSize;
            const centerX = x + this.gridSize / 2;
            const centerY = y + this.gridSize / 2;

            this.ctx.save();
            this.ctx.translate(centerX, centerY);
            this.ctx.rotate(item.animation);

            const size = this.gridSize - 6;
            const pulse = 1 + Math.sin(item.animation * 3) * 0.2;

            switch (item.type) {
                case 'speed':
                    this.drawSpeedItem(size * pulse);
                    break;
                case 'shield':
                    this.drawShieldItem(size * pulse);
                    break;
                case 'score':
                    this.drawScoreItem(size * pulse);
                    break;
            }

            this.ctx.restore();
        }
    }

    drawSpeedItem(size) {
        // 闪电图标
        this.ctx.fillStyle = '#f1c40f';
        this.ctx.strokeStyle = '#f39c12';
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(-size/4, -size/2);
        this.ctx.lineTo(size/4, -size/4);
        this.ctx.lineTo(-size/8, 0);
        this.ctx.lineTo(size/4, size/2);
        this.ctx.lineTo(-size/4, size/4);
        this.ctx.lineTo(size/8, 0);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawShieldItem(size) {
        // 盾牌图标
        this.ctx.fillStyle = '#3498db';
        this.ctx.strokeStyle = '#2980b9';
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(0, -size/2);
        this.ctx.quadraticCurveTo(size/2, -size/3, size/2, 0);
        this.ctx.quadraticCurveTo(size/2, size/3, 0, size/2);
        this.ctx.quadraticCurveTo(-size/2, size/3, -size/2, 0);
        this.ctx.quadraticCurveTo(-size/2, -size/3, 0, -size/2);
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawScoreItem(size) {
        // 钻石图标
        this.ctx.fillStyle = '#e74c3c';
        this.ctx.strokeStyle = '#c0392b';
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(0, -size/2);
        this.ctx.lineTo(size/3, -size/4);
        this.ctx.lineTo(size/2, 0);
        this.ctx.lineTo(size/3, size/4);
        this.ctx.lineTo(0, size/2);
        this.ctx.lineTo(-size/3, size/4);
        this.ctx.lineTo(-size/2, 0);
        this.ctx.lineTo(-size/3, -size/4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawSnake() {
        for (let i = 0; i < this.snakeSegments.length; i++) {
            const segment = this.snakeSegments[i];
            const isHead = i === 0;

            this.ctx.save();
            this.ctx.translate(segment.currentX + this.gridSize/2, segment.currentY + this.gridSize/2);
            this.ctx.scale(segment.scale, segment.scale);

            if (isHead) {
                this.drawSnakeHead();
            } else {
                this.drawSnakeBody(i);
            }

            this.ctx.restore();
        }
    }

    drawSnakeHead() {
        const size = this.gridSize - 2;

        // 头部渐变
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        if (this.hasShield) {
            gradient.addColorStop(0, '#3498db');
            gradient.addColorStop(1, '#2980b9');
        } else if (this.speedBoost) {
            gradient.addColorStop(0, '#f1c40f');
            gradient.addColorStop(1, '#f39c12');
        } else {
            gradient.addColorStop(0, '#2ecc71');
            gradient.addColorStop(1, '#27ae60');
        }

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 眼睛
        this.ctx.fillStyle = '#2c3e50';
        this.ctx.beginPath();
        this.ctx.arc(-size/6, -size/6, 2, 0, Math.PI * 2);
        this.ctx.arc(size/6, -size/6, 2, 0, Math.PI * 2);
        this.ctx.fill();

        // 护盾效果
        if (this.hasShield) {
            this.ctx.strokeStyle = 'rgba(52, 152, 219, 0.5)';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, size/2 + 5, 0, Math.PI * 2);
            this.ctx.stroke();
        }
    }

    drawSnakeBody(index) {
        const size = this.gridSize - 4;
        const intensity = Math.max(0.3, 1 - index * 0.1);

        // 身体渐变
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, size/2);
        gradient.addColorStop(0, `rgba(46, 204, 113, ${intensity})`);
        gradient.addColorStop(1, `rgba(39, 174, 96, ${intensity})`);

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 鳞片纹理
        if (index % 2 === 0) {
            this.ctx.strokeStyle = `rgba(34, 153, 84, ${intensity * 0.5})`;
            this.ctx.lineWidth = 1;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, size/3, 0, Math.PI * 2);
            this.ctx.stroke();
        }
    }

    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;

        this.update();
        this.draw();

        // 根据设置和特殊状态调整速度
        let baseSpeed = this.getGameSpeed();
        if (this.speedBoost) {
            baseSpeed *= 0.5; // 速度提升时更快
        }

        setTimeout(() => this.gameLoop(), baseSpeed);
    }

    getGameSpeed() {
        const speedSettings = {
            slow: 200,
            normal: 150,
            fast: 100
        };

        let speed = speedSettings[this.gameManager.settings.speed] || 150;

        // 根据蛇的长度调整速度
        speed = Math.max(80, speed - this.snake.length);

        return speed;
    }
    
    createParticles(x, y, color, count) {
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = x + 'px';
            particle.style.top = y + 'px';
            particle.style.background = `radial-gradient(circle, ${color}, transparent)`;

            // 随机方向和速度
            const angle = (Math.PI * 2 * i) / count;
            const velocity = 2 + Math.random() * 3;
            const deltaX = Math.cos(angle) * velocity;
            const deltaY = Math.sin(angle) * velocity;

            particle.style.setProperty('--deltaX', deltaX + 'px');
            particle.style.setProperty('--deltaY', deltaY + 'px');

            this.effectsLayer.appendChild(particle);

            // 移除粒子
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 2000);
        }
    }

    createScorePopup(x, y, points) {
        const popup = document.createElement('div');
        popup.className = 'score-popup';
        popup.textContent = `+${points}`;
        popup.style.left = x + 'px';
        popup.style.top = y + 'px';

        this.effectsLayer.appendChild(popup);

        setTimeout(() => {
            if (popup.parentNode) {
                popup.parentNode.removeChild(popup);
            }
        }, 1000);
    }

    createCoinPopup(x, y, coins) {
        const popup = document.createElement('div');
        popup.className = 'coin-popup';
        popup.textContent = `+${coins}🪙`;
        popup.style.left = x + 'px';
        popup.style.top = y + 'px';
        popup.style.cssText += `
            position: absolute;
            color: #f1c40f;
            font-weight: bold;
            font-size: 14px;
            pointer-events: none;
            z-index: 1000;
            animation: coinFloat 1.5s ease-out forwards;
        `;

        this.effectsLayer.appendChild(popup);

        setTimeout(() => {
            if (popup.parentNode) {
                popup.parentNode.removeChild(popup);
            }
        }, 1500);
    }

    createShieldEffect() {
        const head = this.snakeSegments[0];
        if (head) {
            this.createParticles(head.currentX + this.gridSize/2, head.currentY + this.gridSize/2, '#3498db', 15);
        }
    }

    // 新道具效果实现
    teleportSnake() {
        let newX, newY;
        let attempts = 0;

        do {
            newX = Math.floor(Math.random() * this.tileCount);
            newY = Math.floor(Math.random() * this.tileCount);
            attempts++;
        } while (this.isPositionOccupied(newX, newY) && attempts < 100);

        if (attempts < 100) {
            // 创建传送特效
            const oldHead = this.snake[0];
            this.createParticles(oldHead.x * this.gridSize + this.gridSize/2, oldHead.y * this.gridSize + this.gridSize/2, '#9b59b6', 20);

            // 移动蛇头
            this.snake[0] = { x: newX, y: newY };
            this.initializeSnakeSegments();

            // 创建到达特效
            this.createParticles(newX * this.gridSize + this.gridSize/2, newY * this.gridSize + this.gridSize/2, '#e74c3c', 20);
        }
    }

    attractFood() {
        if (!this.food) return;

        const head = this.snake[0];
        const dx = this.food.x - head.x;
        const dy = this.food.y - head.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 5) {
            // 在磁力范围内，食物会慢慢移向蛇头
            if (Math.abs(dx) > Math.abs(dy)) {
                this.food.x += dx > 0 ? -1 : 1;
            } else {
                this.food.y += dy > 0 ? -1 : 1;
            }

            // 创建磁力线效果
            this.createMagnetEffect(head.x * this.gridSize + this.gridSize/2, head.y * this.gridSize + this.gridSize/2);
        }
    }

    explodeBomb(x, y) {
        const explosionRadius = 2;

        // 创建爆炸特效
        this.createExplosionEffect(x * this.gridSize + this.gridSize/2, y * this.gridSize + this.gridSize/2);

        // 清除爆炸范围内的障碍物
        this.obstacles = this.obstacles.filter(obstacle => {
            const distance = Math.sqrt(
                Math.pow(obstacle.x - x, 2) + Math.pow(obstacle.y - y, 2)
            );
            return distance > explosionRadius;
        });

        // 给玩家额外分数
        this.score += 50;
    }

    createMagnetEffect(x, y) {
        const effect = document.createElement('div');
        effect.style.position = 'absolute';
        effect.style.left = x + 'px';
        effect.style.top = y + 'px';
        effect.style.width = '4px';
        effect.style.height = '4px';
        effect.style.background = '#f1c40f';
        effect.style.borderRadius = '50%';
        effect.style.pointerEvents = 'none';
        effect.style.zIndex = '1000';
        effect.style.animation = 'magnetPulse 0.5s ease-out forwards';

        this.effectsLayer.appendChild(effect);

        setTimeout(() => {
            if (effect.parentNode) {
                effect.parentNode.removeChild(effect);
            }
        }, 500);
    }

    createExplosionEffect(x, y) {
        for (let i = 0; i < 16; i++) {
            const particle = document.createElement('div');
            particle.style.position = 'absolute';
            particle.style.left = x + 'px';
            particle.style.top = y + 'px';
            particle.style.width = '6px';
            particle.style.height = '6px';
            particle.style.background = i % 2 === 0 ? '#e74c3c' : '#f39c12';
            particle.style.borderRadius = '50%';
            particle.style.pointerEvents = 'none';
            particle.style.zIndex = '1000';

            const angle = (i / 16) * Math.PI * 2;
            const distance = 30 + Math.random() * 20;
            const deltaX = Math.cos(angle) * distance;
            const deltaY = Math.sin(angle) * distance;

            particle.style.setProperty('--deltaX', deltaX + 'px');
            particle.style.setProperty('--deltaY', deltaY + 'px');
            particle.style.animation = 'explosion 0.8s ease-out forwards';

            this.effectsLayer.appendChild(particle);

            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 800);
        }
    }

    updateParticles() {
        // 粒子效果在CSS动画中处理
    }

    updateAnimations() {
        // 动画效果在CSS动画中处理
    }

    playSound(type) {
        if (!this.gameManager.settings.sound) return;

        // 使用Web Audio API创建音效
        try {
            const audioContext = this.getAudioContext();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // 根据音效类型设置不同的频率和持续时间
            switch (type) {
                case 'eat':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.1);
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.1);
                    break;
                case 'powerup':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.2);
                    break;
                case 'gameover':
                    oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.5);
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.5);
                    break;
                case 'levelup':
                    // 播放一个上升的音阶
                    const frequencies = [523, 659, 784, 1047]; // C5, E5, G5, C6
                    frequencies.forEach((freq, index) => {
                        const osc = audioContext.createOscillator();
                        const gain = audioContext.createGain();
                        osc.connect(gain);
                        gain.connect(audioContext.destination);

                        osc.frequency.setValueAtTime(freq, audioContext.currentTime + index * 0.1);
                        gain.gain.setValueAtTime(0.2, audioContext.currentTime + index * 0.1);
                        gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + index * 0.1 + 0.2);

                        osc.start(audioContext.currentTime + index * 0.1);
                        osc.stop(audioContext.currentTime + index * 0.1 + 0.2);
                    });
                    break;
                case 'start':
                    oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(880, audioContext.currentTime + 0.3);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.3);
                    break;
                case 'pause':
                    oscillator.frequency.setValueAtTime(300, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.1);
                    break;
                case 'shield':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.15);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.15);
                    break;
                case 'warning':
                    // 警告音效 - 快速的高低音交替
                    for (let i = 0; i < 3; i++) {
                        const osc = audioContext.createOscillator();
                        const gain = audioContext.createGain();
                        osc.connect(gain);
                        gain.connect(audioContext.destination);

                        osc.frequency.setValueAtTime(800, audioContext.currentTime + i * 0.2);
                        osc.frequency.setValueAtTime(400, audioContext.currentTime + i * 0.2 + 0.1);
                        gain.gain.setValueAtTime(0.3, audioContext.currentTime + i * 0.2);
                        gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + i * 0.2 + 0.2);

                        osc.start(audioContext.currentTime + i * 0.2);
                        osc.stop(audioContext.currentTime + i * 0.2 + 0.2);
                    }
                    break;
            }
        } catch (error) {
            console.log(`音效播放失败: ${type}`, error);
        }
    }

    getAudioContext() {
        if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        return this.audioContext;
    }

    vibrate(duration) {
        if (this.gameManager.settings.vibration && navigator.vibrate) {
            navigator.vibrate(duration);
        }
    }

    showAchievement(text) {
        const achievement = document.createElement('div');
        achievement.className = 'achievement';
        achievement.textContent = text;

        document.body.appendChild(achievement);

        setTimeout(() => {
            if (achievement.parentNode) {
                achievement.parentNode.removeChild(achievement);
            }
        }, 3000);
    }

    gameOver() {
        this.gameRunning = false;
        this.playSound('gameover');
        this.vibrate(200); // 强烈振动
        this.updateButtons();

        // 检查是否创造新记录
        const isNewRecord = this.checkNewRecord();

        if (isNewRecord) {
            this.showNameInputModal();
        } else {
            this.showGameOverModal();
        }
    }

    checkNewRecord() {
        const leaderboard = this.gameManager.leaderboards[this.gameManager.gameMode];
        return leaderboard.length < 10 || this.score > leaderboard[leaderboard.length - 1].score;
    }

    showNameInputModal() {
        const modal = document.createElement('div');
        modal.className = 'name-input-modal';
        modal.innerHTML = `
            <h2>🎉 新记录！</h2>
            <p class="final-score">得分: ${this.score}</p>
            <p>请输入您的姓名：</p>
            <input type="text" id="playerName" placeholder="输入姓名" maxlength="10">
            <br>
            <button id="saveScoreBtn">保存</button>
            <button id="skipSaveBtn">跳过</button>
        `;

        document.body.appendChild(modal);

        // 添加事件监听器
        document.getElementById('saveScoreBtn').addEventListener('click', () => {
            this.saveScore();
        });

        document.getElementById('skipSaveBtn').addEventListener('click', () => {
            this.skipSave();
        });

        // 回车键保存
        const input = document.getElementById('playerName');
        if (input) {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.saveScore();
                }
            });

            // 聚焦输入框
            setTimeout(() => {
                input.focus();
            }, 100);
        }
    }

    saveScore() {
        const nameInput = document.getElementById('playerName');
        const playerName = nameInput ? nameInput.value.trim() || '匿名玩家' : '匿名玩家';

        const scoreData = {
            name: playerName,
            score: this.score,
            level: this.level,
            length: this.snake.length,
            time: this.gameTime,
            date: new Date().toLocaleDateString()
        };

        this.gameManager.addToLeaderboard(this.gameManager.gameMode, scoreData);

        // 移除输入模态框
        const modal = document.querySelector('.name-input-modal');
        if (modal) modal.remove();

        this.showGameOverModal();
    }

    skipSave() {
        const modal = document.querySelector('.name-input-modal');
        if (modal) modal.remove();
        this.showGameOverModal();
    }

    showGameOverModal() {
        const gameOverDiv = document.createElement('div');
        gameOverDiv.className = 'game-over';

        // 计算一些统计数据
        const avgScorePerFood = this.stats.foodEaten > 0 ? Math.round(this.score / this.stats.foodEaten) : 0;
        const gameMode = this.gameManager.gameMode;
        const modeText = {
            'classic': '经典模式',
            'adventure': '冒险模式',
            'timeattack': '限时模式'
        }[gameMode] || '未知模式';

        gameOverDiv.innerHTML = `
            <h2>游戏结束!</h2>
            <div class="game-stats">
                <div class="stat-row">
                    <span class="stat-label">游戏模式:</span>
                    <span class="stat-value">${modeText}</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">最终得分:</span>
                    <span class="stat-value final-score">${this.score}</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">游戏时长:</span>
                    <span class="stat-value">${Math.floor(this.gameTime / 60)}:${(this.gameTime % 60).toString().padStart(2, '0')}</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">蛇身长度:</span>
                    <span class="stat-value">${this.snake.length}</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">达到关卡:</span>
                    <span class="stat-value">${this.level}</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">食物数量:</span>
                    <span class="stat-value">${this.stats.foodEaten}</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">道具收集:</span>
                    <span class="stat-value">${this.stats.powerupsCollected}</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">平均每食物得分:</span>
                    <span class="stat-value">${avgScorePerFood}</span>
                </div>
            </div>
            <div class="game-over-buttons">
                <button id="restartGameBtn">重新开始</button>
                <button id="showLeaderboardBtn">查看排行榜</button>
                <button id="backToMenuBtn">返回主菜单</button>
            </div>
        `;
        document.body.appendChild(gameOverDiv);

        // 添加事件监听器
        document.getElementById('restartGameBtn').addEventListener('click', () => {
            this.resetGame();
        });

        document.getElementById('showLeaderboardBtn').addEventListener('click', () => {
            gameManager.showLeaderboard();
        });

        document.getElementById('backToMenuBtn').addEventListener('click', () => {
            gameManager.showMainMenu();
        });
    }
}

// 扩展游戏管理器方法
GameManager.prototype.showMainMenu = function() {
    this.showScreen('mainMenu');
};

GameManager.prototype.showGameMode = function() {
    this.showScreen('gameModeMenu');
};

GameManager.prototype.showLeaderboard = function() {
    this.showScreen('leaderboardScreen');
    this.updateLeaderboardDisplay('classic');
};

GameManager.prototype.showSettings = function() {
    this.showScreen('settingsScreen');
    this.updateSettingsDisplay();
};

GameManager.prototype.showInstructions = function() {
    this.showScreen('instructionsScreen');
};

GameManager.prototype.startClassicMode = function() {
    this.gameMode = 'classic';
    this.startGame();
};

GameManager.prototype.startAdventureMode = function() {
    this.gameMode = 'adventure';
    this.startGame();
};

GameManager.prototype.startTimeAttackMode = function() {
    this.gameMode = 'timeattack';
    this.startGame();
};

GameManager.prototype.startGame = function() {
    this.showScreen('gameScreen');
    if (window.game) {
        window.game.resetGame();
    } else {
        window.game = new EnhancedSnakeGame(this);
    }
};

GameManager.prototype.addToLeaderboard = function(mode, scoreData) {
    if (!this.leaderboards[mode]) {
        this.leaderboards[mode] = [];
    }

    this.leaderboards[mode].push(scoreData);
    this.leaderboards[mode].sort((a, b) => b.score - a.score);
    this.leaderboards[mode] = this.leaderboards[mode].slice(0, 10); // 保留前10名

    this.saveLeaderboards();
};

GameManager.prototype.updateLeaderboardDisplay = function(mode) {
    const content = document.getElementById('leaderboardContent');
    const leaderboard = this.leaderboards[mode] || [];

    // 更新标签页
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[onclick="showLeaderboardTab('${mode}')"]`).classList.add('active');

    if (leaderboard.length === 0) {
        content.innerHTML = '<p style="text-align: center; opacity: 0.7;">暂无记录</p>';
        return;
    }

    content.innerHTML = leaderboard.map((entry, index) => `
        <div class="leaderboard-item rank-${index + 1}">
            <div class="rank">${index + 1}</div>
            <div class="player-info">
                <div class="player-name">${entry.name}</div>
                <div class="player-details">
                    关卡 ${entry.level} • 长度 ${entry.length} • ${entry.time}秒 • ${entry.date}
                </div>
            </div>
            <div class="score-info">
                <div class="score-value">${entry.score}</div>
            </div>
        </div>
    `).join('');
};

GameManager.prototype.updateSettingsDisplay = function() {
    document.getElementById('soundToggle').checked = this.settings.sound;
    document.getElementById('speedSelect').value = this.settings.speed;
    document.getElementById('themeSelect').value = this.settings.theme;
    document.getElementById('particleToggle').checked = this.settings.particles;
    document.getElementById('vibrationToggle').checked = this.settings.vibration;
    document.getElementById('trailToggle').checked = this.settings.trail;

    // 添加事件监听器
    document.getElementById('soundToggle').onchange = (e) => {
        this.settings.sound = e.target.checked;
        this.saveSettings();
    };

    document.getElementById('speedSelect').onchange = (e) => {
        this.settings.speed = e.target.value;
        this.saveSettings();
    };

    document.getElementById('themeSelect').onchange = (e) => {
        this.settings.theme = e.target.value;
        this.saveSettings();
        this.applyTheme();
    };

    document.getElementById('particleToggle').onchange = (e) => {
        this.settings.particles = e.target.checked;
        this.saveSettings();
    };

    document.getElementById('vibrationToggle').onchange = (e) => {
        this.settings.vibration = e.target.checked;
        this.saveSettings();
    };

    document.getElementById('trailToggle').onchange = (e) => {
        this.settings.trail = e.target.checked;
        this.saveSettings();
    };
};

GameManager.prototype.applyTheme = function() {
    // 移除所有主题类
    document.body.classList.remove('theme-classic', 'theme-neon', 'theme-nature', 'theme-cyberpunk', 'theme-retro', 'theme-galaxy');
    // 应用新主题
    document.body.classList.add(`theme-${this.settings.theme}`);

    // 主题特定的设置
    switch(this.settings.theme) {
        case 'neon':
            document.body.style.setProperty('--primary-color', '#ff006e');
            document.body.style.setProperty('--secondary-color', '#8338ec');
            break;
        case 'cyberpunk':
            document.body.style.setProperty('--primary-color', '#00ffff');
            document.body.style.setProperty('--secondary-color', '#ff0080');
            break;
        case 'galaxy':
            document.body.style.setProperty('--primary-color', '#9b59b6');
            document.body.style.setProperty('--secondary-color', '#3498db');
            break;
        case 'retro':
            document.body.style.setProperty('--primary-color', '#f39c12');
            document.body.style.setProperty('--secondary-color', '#e74c3c');
            break;
        default:
            document.body.style.setProperty('--primary-color', '#2ecc71');
            document.body.style.setProperty('--secondary-color', '#3498db');
    }
};

// 全局函数
function showGameMode() {
    gameManager.showGameMode();
}

function showLeaderboard() {
    gameManager.showLeaderboard();
}

function showSettings() {
    gameManager.showSettings();
}

function showInstructions() {
    gameManager.showInstructions();
}

function showMainMenu() {
    gameManager.showMainMenu();
}

function startClassicMode() {
    gameManager.startClassicMode();
}

function startAdventureMode() {
    gameManager.startAdventureMode();
}

function startTimeAttackMode() {
    gameManager.startTimeAttackMode();
}

function showLeaderboardTab(mode) {
    gameManager.updateLeaderboardDisplay(mode);
}

function showGameMode() {
    gameManager.showGameMode();
}

function showLeaderboard() {
    gameManager.showLeaderboard();
}

function showSettings() {
    gameManager.showSettings();
}

function showInstructions() {
    gameManager.showInstructions();
}

function showSkinShop() {
    gameManager.showSkinShop();
}

function showLevelEditor() {
    gameManager.showLevelEditor();
}

function startMultiplayerMode() {
    gameManager.showScreen('multiplayerScreen');
}

function startDailyChallengeMode() {
    gameManager.showDailyChallengeScreen();
}

function startSurvivalMode() {
    gameManager.startSurvivalMode();
}

function startLocalMultiplayer() {
    gameManager.startLocalMultiplayer();
}

function createRoom() {
    gameManager.createRoom();
}

function joinRoom() {
    gameManager.joinRoom();
}

function startDailyChallenge() {
    gameManager.startDailyChallenge();
}

// 关卡编辑器函数
function clearLevel() {
    if (gameManager.levelEditor) {
        gameManager.levelEditor.clearLevel();
    }
}

function saveLevel() {
    if (gameManager.levelEditor) {
        gameManager.levelEditor.saveLevel();
    }
}

function loadLevel() {
    if (gameManager.levelEditor) {
        gameManager.levelEditor.loadLevel();
    }
}

function testLevel() {
    if (gameManager.levelEditor) {
        gameManager.levelEditor.testLevel();
    }
}

// 错误处理
window.addEventListener('error', (e) => {
    console.error('游戏发生错误:', e.error);
    // 可以在这里添加错误报告逻辑
});

// 页面可见性API - 当页面不可见时自动暂停游戏
document.addEventListener('visibilitychange', () => {
    if (document.hidden && window.game && window.game.gameRunning && !window.game.gamePaused) {
        window.game.togglePause();
    }
});

// 防止页面刷新时丢失游戏状态
window.addEventListener('beforeunload', (e) => {
    if (window.game && window.game.gameRunning) {
        e.preventDefault();
        e.returnValue = '游戏正在进行中，确定要离开吗？';
        return e.returnValue;
    }
});

// 初始化游戏管理器
const gameManager = new GameManager();
let game = null;

// 性能监控
let lastFrameTime = 0;
function monitorPerformance() {
    const now = performance.now();
    const deltaTime = now - lastFrameTime;
    lastFrameTime = now;

    // 如果帧率太低，可以降低游戏质量
    if (deltaTime > 50) { // 低于20FPS
        console.warn('性能警告: 帧率较低');
    }

    requestAnimationFrame(monitorPerformance);
}

// 开始性能监控
requestAnimationFrame(monitorPerformance);
