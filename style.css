* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

/* 动画背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes backgroundShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

/* 菜单屏幕 */
.menu-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    transition: all 0.5s ease;
}

.menu-screen.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.9);
}

.menu-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 25px;
    backdrop-filter: blur(15px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    max-width: 600px;
    width: 90%;
}

.game-title {
    font-size: 3em;
    margin-bottom: 30px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 菜单按钮 */
.menu-buttons, .mode-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.multiplayer-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.menu-btn, .mode-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.menu-btn::before, .mode-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.menu-btn:hover::before, .mode-btn:hover::before {
    left: 100%;
}

.menu-btn:hover, .mode-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.mode-btn {
    text-align: left;
    padding: 20px;
}

.mode-btn h3 {
    margin-bottom: 5px;
    font-size: 20px;
}

.mode-btn p {
    font-size: 14px;
    opacity: 0.8;
    font-weight: normal;
}

.back-btn {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
}

/* 游戏屏幕 */
.game-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 50;
    transition: all 0.5s ease;
}

.game-screen.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.9);
}

.game-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.game-header {
    margin-bottom: 20px;
}

.game-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.info-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-item .label {
    font-size: 12px;
    opacity: 0.8;
    margin-right: 5px;
}

.info-item .value {
    font-size: 16px;
    font-weight: bold;
    color: #4ecdc4;
    transition: all 0.3s ease;
}

/* 分数更新动画 */
@keyframes scoreUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #f1c40f; }
    100% { transform: scale(1); }
}

.game-status {
    color: #f39c12;
    font-size: 16px;
    text-align: center;
    padding: 10px;
    background: rgba(243, 156, 18, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

/* 游戏区域 */
.game-area {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

#gameCanvas {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.3),
        inset 0 0 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

#gameCanvas:hover {
    box-shadow:
        0 0 40px rgba(0, 255, 255, 0.5),
        inset 0 0 30px rgba(0, 0, 0, 0.3);
}

.effects-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: 15px;
    overflow: hidden;
}

/* 粒子效果 */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #4ecdc4, transparent);
    border-radius: 50%;
    animation: particleFloat 2s ease-out forwards;
}

@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(0.5) translateY(-50px);
    }
}

.score-popup {
    position: absolute;
    color: #4ecdc4;
    font-weight: bold;
    font-size: 20px;
    text-shadow: 0 0 10px #4ecdc4;
    animation: scorePopup 1s ease-out forwards;
    pointer-events: none;
}

@keyframes scorePopup {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(1.5) translateY(-30px);
    }
}

/* 游戏控制按钮 */
.game-controls {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.control-button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.control-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.control-button:hover::before {
    left: 100%;
}

.control-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.control-button:active {
    transform: translateY(0);
}

.control-button:disabled {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    cursor: not-allowed;
    transform: none;
}

.control-button:disabled::before {
    display: none;
}

/* 键盘提示 */
.keyboard-hints {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.hint-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 12px;
}

.hint-item .key {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 11px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hint-item .action {
    opacity: 0.8;
}

/* 排行榜样式 */
.leaderboard-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-btn.active {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.leaderboard-content {
    max-height: 400px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border-left: 4px solid #4ecdc4;
}

.leaderboard-item.rank-1 { border-left-color: #f1c40f; }
.leaderboard-item.rank-2 { border-left-color: #95a5a6; }
.leaderboard-item.rank-3 { border-left-color: #e67e22; }

.rank {
    font-size: 18px;
    font-weight: bold;
    width: 30px;
}

.player-info {
    flex: 1;
    text-align: left;
    margin-left: 15px;
}

.player-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.player-details {
    font-size: 12px;
    opacity: 0.7;
}

.score-info {
    text-align: right;
}

.score-value {
    font-size: 18px;
    font-weight: bold;
    color: #4ecdc4;
}

/* 设置界面 */
.settings-content {
    text-align: left;
    max-width: 400px;
    margin: 0 auto;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.setting-item label {
    font-weight: bold;
}

.setting-item input, .setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    padding: 5px 10px;
    color: white;
}

/* 游戏说明 */
.instructions-content {
    text-align: left;
    max-width: 500px;
    margin: 0 auto;
}

.instruction-section {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #4ecdc4;
}

.instruction-section h3 {
    margin-bottom: 15px;
    color: #4ecdc4;
    font-size: 18px;
}

.instruction-section p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.5;
}

/* 移动端控制 */
.mobile-controls {
    display: none;
    margin-top: 20px;
}

.control-row {
    margin: 5px 0;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.control-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.control-btn:active {
    transform: scale(0.95);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .menu-container {
        padding: 30px 20px;
        margin: 20px;
    }

    .game-title {
        font-size: 2.2em;
    }

    .game-container {
        padding: 15px;
        margin: 10px;
    }

    #gameCanvas {
        width: 350px;
        height: 350px;
    }

    .mobile-controls {
        display: block;
    }

    .game-info {
        flex-direction: column;
        gap: 10px;
    }

    .info-item {
        width: 100%;
        text-align: center;
    }

    .control-button {
        padding: 10px 15px;
        font-size: 12px;
    }

    .leaderboard-tabs {
        flex-direction: column;
        gap: 5px;
    }

    .tab-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 1.8em;
    }

    #gameCanvas {
        width: 280px;
        height: 280px;
    }

    .menu-btn, .mode-btn {
        font-size: 16px;
        padding: 12px 20px;
    }

    .control-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* 游戏结束弹窗 */
.game-over {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(26, 26, 46, 0.95));
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    z-index: 1000;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    animation: gameOverAppear 0.5s ease-out;
}

@keyframes gameOverAppear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.game-over h2 {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 20px;
    font-size: 2.5em;
    text-shadow: 0 0 20px rgba(231, 76, 60, 0.5);
}

.game-over p {
    margin: 15px 0;
    font-size: 1.3em;
}

.game-over .final-score {
    font-size: 1.8em;
    color: #4ecdc4;
    font-weight: bold;
    text-shadow: 0 0 15px rgba(78, 205, 196, 0.5);
}

.game-over button {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 15px 30px;
    margin: 10px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.game-over button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(39, 174, 96, 0.4);
}

/* 游戏统计样式 */
.game-stats {
    margin: 20px 0;
    text-align: left;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 14px;
    opacity: 0.8;
}

.stat-value {
    font-weight: bold;
    color: #4ecdc4;
}

.stat-value.final-score {
    font-size: 18px;
    color: #f1c40f;
    text-shadow: 0 0 10px rgba(241, 196, 15, 0.5);
}

.game-over-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.game-over-buttons button {
    width: 100%;
}

/* 暂停覆盖层 */
.pause-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.pause-content {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 26, 46, 0.9));
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
}

.pause-content h2 {
    margin-bottom: 20px;
    font-size: 2em;
    color: #f39c12;
}

.pause-content p {
    margin-bottom: 20px;
    opacity: 0.8;
}

.pause-stats {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
}

.pause-stats div {
    margin: 5px 0;
    font-weight: bold;
    color: #4ecdc4;
}

.pause-content button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    margin: 5px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.pause-content button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* 名字输入弹窗 */
.name-input-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(26, 26, 46, 0.95));
    color: white;
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    z-index: 1001;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
}

.name-input-modal input {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 12px 20px;
    color: white;
    font-size: 16px;
    margin: 15px 0;
    width: 200px;
    text-align: center;
}

.name-input-modal input:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #4ecdc4;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成就通知 */
.achievement {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    animation: achievementSlide 3s ease-in-out;
}

@keyframes achievementSlide {
    0%, 100% { transform: translateX(100%); opacity: 0; }
    10%, 90% { transform: translateX(0); opacity: 1; }
}

/* 收集效果动画 */
@keyframes collectEffect {
    0% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
    50% {
        transform: scale(1.5) translateY(-20px);
        opacity: 0.8;
    }
    100% {
        transform: scale(2) translateY(-40px);
        opacity: 0;
    }
}

/* 蛇身呼吸效果 */
@keyframes snakeBreath {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 食物脉动效果 */
@keyframes foodPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 护盾效果 */
@keyframes shieldGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);
        transform: scale(1.02);
    }
}

/* 速度提升效果 */
@keyframes speedBoost {
    0%, 100% {
        box-shadow: 0 0 10px rgba(241, 196, 15, 0.5);
    }
    50% {
        box-shadow: 0 0 25px rgba(241, 196, 15, 0.9);
    }
}

/* 关卡完成庆祝效果 */
@keyframes levelComplete {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(5deg); }
    50% { transform: scale(1.2) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(3deg); }
    100% { transform: scale(1) rotate(0deg); }
}

/* 游戏结束震动效果 */
@keyframes gameOverShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* 新记录闪烁效果 */
@keyframes newRecordBlink {
    0%, 100% {
        color: #4ecdc4;
        text-shadow: 0 0 10px #4ecdc4;
    }
    50% {
        color: #f1c40f;
        text-shadow: 0 0 20px #f1c40f;
    }
}

/* 主题切换 */
.theme-neon {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a0a2e 50%, #0f0f23 100%);
}

.theme-neon .game-container,
.theme-neon .menu-container {
    border: 2px solid #00ffff;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
}

.theme-nature {
    background: linear-gradient(135deg, #2d5016 0%, #3e6b1f 50%, #4a7c23 100%);
}

.theme-nature .game-container,
.theme-nature .menu-container {
    border: 2px solid #8bc34a;
    box-shadow: 0 0 30px rgba(139, 195, 74, 0.3);
}

/* 加载动画 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: white;
}

.snake-loader {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-bottom: 20px;
}

.snake-segment {
    width: 15px;
    height: 15px;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    border-radius: 50%;
    animation: snakeMove 1.5s ease-in-out infinite;
}

.snake-segment:nth-child(1) { animation-delay: 0s; }
.snake-segment:nth-child(2) { animation-delay: 0.2s; }
.snake-segment:nth-child(3) { animation-delay: 0.4s; }
.snake-segment:nth-child(4) { animation-delay: 0.6s; }

@keyframes snakeMove {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
}

.loading-content h2 {
    font-size: 2.5em;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease-in-out infinite;
}

.loading-content p {
    font-size: 1.2em;
    opacity: 0.8;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* 皮肤商店样式 */
.shop-content {
    text-align: center;
}

.currency-display {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 15px;
    margin-bottom: 20px;
    font-size: 1.2em;
}

.currency-value {
    color: #f1c40f;
    font-weight: bold;
    margin-left: 10px;
}

.skin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.skin-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.skin-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.skin-item.selected {
    border-color: #2ecc71;
    background: rgba(46, 204, 113, 0.2);
}

.skin-item.locked {
    opacity: 0.6;
}

.skin-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 auto 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.skin-price {
    font-size: 0.9em;
    color: #f1c40f;
    margin-top: 5px;
}

/* 关卡编辑器样式 */
.level-editor-container {
    max-width: 800px;
    width: 95%;
}

.editor-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.tool-palette {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.tool-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.tool-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.tool-btn.active {
    background: #3498db;
    color: white;
}

.editor-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.action-btn:hover {
    background: #2ecc71;
}

.editor-canvas-container {
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    overflow: hidden;
    margin: 20px 0;
}

/* 每日挑战样式 */
.challenge-info {
    text-align: center;
}

.challenge-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.challenge-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 10px;
    font-weight: bold;
}

.challenge-status.completed {
    background: rgba(46, 204, 113, 0.3);
    color: #2ecc71;
}

.challenge-status.pending {
    background: rgba(241, 196, 15, 0.3);
    color: #f1c40f;
}

.challenge-rewards {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
}

.reward-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.reward-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 10px;
    font-size: 0.9em;
}

.challenge-start-btn {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    margin: 20px 10px;
    transition: all 0.3s ease;
}

.challenge-start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);
}

/* 多人游戏样式 */
.multiplayer-scores {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    gap: 20px;
}

.player-score {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 15px;
    flex: 1;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(46, 204, 113, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    z-index: 10000;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 新特效动画 */
@keyframes magnetPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

@keyframes explosion {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--deltaX), var(--deltaY)) scale(0);
        opacity: 0;
    }
}

@keyframes collectEffect {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes coinFloat {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-30px);
        opacity: 0;
    }
}

/* 响应式设计改进 */
@media (max-width: 768px) {
    .skin-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .editor-controls {
        flex-direction: column;
    }

    .tool-palette, .editor-actions {
        justify-content: center;
    }

    .multiplayer-scores {
        flex-direction: column;
    }
}
